#!/usr/bin/env python3
"""
使用真实回测数据测试grok模型反思分析师
重现实际回测中出现的问题
"""

import json
import os
import sys
from datetime import datetime
from typing import Dict, Any

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.agents.reflection_analyst import generate_reflection_analysis, ReflectionAnalysis


def load_real_backtest_data():
    """加载真实的回测数据"""
    print("📂 加载真实回测数据...")
    
    # 加载投资组合决策
    portfolio_file = "reasoning_logs/experiment_2025-06-27/2025-01-01/2025-01-01_NVDA_portfolio_manager_agent_181648221.json"
    with open(portfolio_file, 'r', encoding='utf-8') as f:
        portfolio_data = json.load(f)
    
    decision = portfolio_data["reasoning"]
    print(f"✅ 投资组合决策加载成功")
    print(f"   动作: {decision['action']}")
    print(f"   数量: {decision['quantity']}")
    print(f"   信心: {decision['confidence']}")
    
    # 加载分析师信号
    input_data_dir = "reasoning_logs/experiment_2025-06-27/2025-01-01/input_data"
    analyst_signals = {}
    
    # 需要加载的分析师列表（排除风险管理和反思分析师）
    analysts_to_load = [
        "fundamentals_analyst_agent",
        "market_analyst_agent", 
        "news_analyst_agent",
        "aswath_damodaran_agent",
        "ben_graham_agent",
        "bill_ackman_agent",
        "cathie_wood_agent",
        "charlie_munger_agent",
        "michael_burry_agent",
        "peter_lynch_agent",
        "phil_fisher_agent",
        "stanley_druckenmiller_agent",
        "warren_buffett_agent"
    ]
    
    for analyst in analysts_to_load:
        file_path = f"{input_data_dir}/{analyst}_NVDA_2025-01-01.json"
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                analyst_signals[analyst] = data
        else:
            print(f"⚠️ 文件不存在: {file_path}")
    
    print(f"✅ 分析师信号加载成功，共 {len(analyst_signals)} 个分析师")
    
    return decision, analyst_signals


def test_with_real_data():
    """使用真实数据测试反思分析"""
    print("🔍 使用真实数据测试grok模型反思分析")
    print("=" * 50)
    
    # 加载真实数据
    decision, analyst_signals = load_real_backtest_data()
    
    model_name = "grok-beta"
    model_provider = "QingYun"
    
    try:
        print("1️⃣ 调用反思分析函数...")
        print(f"   决策数据大小: {len(json.dumps(decision))} 字符")
        print(f"   分析师信号数据大小: {len(json.dumps(analyst_signals))} 字符")
        
        result = generate_reflection_analysis(
            ticker="NVDA",
            decision=decision,
            analyst_signals=analyst_signals,
            model_name=model_name,
            model_provider=model_provider
        )
        
        print(f"✅ 反思分析调用成功")
        print(f"   结果类型: {type(result)}")
        print(f"   决策质量: {result.decision_quality}")
        print(f"   正确性评分: {result.correctness_score}")
        print(f"   关键洞察数量: {len(result.key_insights)}")
        print(f"   推荐建议数量: {len(result.recommendations)}")
        print(f"   推理过程长度: {len(result.reasoning)} 字符")
        
        # 检查是否是默认错误响应
        if result.reasoning == "Error occurred during reflection analysis process, defaulting to fair evaluation":
            print("❌ 检测到默认错误响应")
            return False
        else:
            print("✅ 获得了有效的分析结果")
            
            # 显示详细结果
            print("\n📋 详细分析结果:")
            print(f"决策质量: {result.decision_quality}")
            print(f"正确性评分: {result.correctness_score}")
            print("\n关键洞察:")
            for i, insight in enumerate(result.key_insights, 1):
                print(f"  {i}. {insight}")
            print("\n推荐建议:")
            for i, rec in enumerate(result.recommendations, 1):
                print(f"  {i}. {rec}")
            print(f"\n推理过程:\n{result.reasoning}")
            
            return True
            
    except Exception as e:
        print(f"❌ 反思分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_data_size_impact():
    """测试数据大小对结果的影响"""
    print("\n📊 测试数据大小对结果的影响")
    print("=" * 50)
    
    # 加载真实数据
    decision, analyst_signals = load_real_backtest_data()
    
    model_name = "grok-beta"
    model_provider = "QingYun"
    
    # 测试不同的数据大小
    test_cases = [
        ("完整数据", decision, analyst_signals),
        ("简化决策", {"action": decision["action"], "confidence": decision["confidence"]}, analyst_signals),
        ("少量分析师", decision, dict(list(analyst_signals.items())[:3])),
        ("最小数据", {"action": "hold", "confidence": 60.0}, {"fundamentals_analyst_agent": list(analyst_signals.values())[0]})
    ]
    
    results = {}
    
    for test_name, test_decision, test_signals in test_cases:
        print(f"\n🧪 测试: {test_name}")
        print(f"   决策数据大小: {len(json.dumps(test_decision))} 字符")
        print(f"   分析师信号数据大小: {len(json.dumps(test_signals))} 字符")
        
        try:
            result = generate_reflection_analysis(
                ticker="NVDA",
                decision=test_decision,
                analyst_signals=test_signals,
                model_name=model_name,
                model_provider=model_provider
            )
            
            is_error = result.reasoning == "Error occurred during reflection analysis process, defaulting to fair evaluation"
            results[test_name] = {
                "success": not is_error,
                "decision_quality": result.decision_quality,
                "correctness_score": result.correctness_score,
                "reasoning_length": len(result.reasoning)
            }
            
            status = "❌ 错误响应" if is_error else "✅ 成功"
            print(f"   结果: {status}")
            
        except Exception as e:
            print(f"   结果: ❌ 异常 - {e}")
            results[test_name] = {"success": False, "error": str(e)}
    
    # 总结结果
    print("\n📊 测试结果总结:")
    for test_name, result in results.items():
        if result.get("success"):
            print(f"✅ {test_name}: 质量={result['decision_quality']}, 评分={result['correctness_score']}")
        else:
            print(f"❌ {test_name}: 失败")
    
    return results


def main():
    """主测试函数"""
    print("🚀 使用真实数据测试Grok模型反思分析师")
    print(f"⏰ 测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 检查环境变量
    if not os.getenv("QINGYUN_API_KEY"):
        print("❌ QINGYUN_API_KEY环境变量未设置")
        return
    
    # 检查数据文件是否存在
    portfolio_file = "reasoning_logs/experiment_2025-06-27/2025-01-01/2025-01-01_NVDA_portfolio_manager_agent_181648221.json"
    if not os.path.exists(portfolio_file):
        print(f"❌ 数据文件不存在: {portfolio_file}")
        return
    
    test_results = {}
    
    # 测试1: 使用真实数据
    test_results["real_data"] = test_with_real_data()
    
    # 测试2: 数据大小影响
    size_results = test_data_size_impact()
    test_results["size_impact"] = all(r.get("success", False) for r in size_results.values())
    
    # 总结结果
    print("\n📊 最终测试结果总结")
    print("=" * 30)
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    overall_success = all(test_results.values())
    print(f"\n🎯 总体结果: {'✅ 所有测试通过' if overall_success else '❌ 存在失败的测试'}")


if __name__ == "__main__":
    main()
