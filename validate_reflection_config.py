#!/usr/bin/env python3
"""
反思代理配置验证脚本
检查环境配置、API密钥和模型可用性
"""

import os
import sys
import json
from typing import Dict, List, Tuple

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def check_environment_variables() -> Dict[str, bool]:
    """检查环境变量配置"""
    required_vars = {
        "DEEPSEEK_API_KEY": "DeepSeek API密钥",
        "OPENAI_API_KEY": "OpenAI API密钥", 
        "QINGYUN_API_KEY": "QingYun API密钥",
        "OPENROUTER_API_KEY": "OpenRouter API密钥",
        "GROQ_API_KEY1": "GROQ API密钥1",
        "GROQ_API_KEY2": "GROQ API密钥2",
        "GROQ_API_KEY3": "GROQ API密钥3",
        "GROQ_API_KEY4": "GROQ API密钥4"
    }
    
    results = {}
    print("🔍 检查环境变量配置:")
    
    for var_name, description in required_vars.items():
        value = os.getenv(var_name)
        is_configured = bool(value and value.strip() and not value.startswith("your-"))
        results[var_name] = is_configured
        
        status = "✅" if is_configured else "❌"
        print(f"  {status} {description} ({var_name})")
        
        if is_configured and len(value) > 10:
            # 显示密钥的前几位和后几位
            masked_key = f"{value[:8]}...{value[-4:]}"
            print(f"      密钥: {masked_key}")
    
    return results


def test_model_connections() -> Dict[str, Dict[str, any]]:
    """测试模型连接"""
    print("\n🔗 测试模型连接:")
    
    test_configs = [
        {"name": "DeepSeek Chat", "model": "deepseek-chat", "provider": "deepseek"},
        {"name": "GPT-3.5 Turbo", "model": "gpt-3.5-turbo", "provider": "openai"},
        {"name": "Gemini 2.0 Flash", "model": "gemini-2.0-flash-exp", "provider": "qingyun"},
        {"name": "Llama 4 Scout", "model": "meta-llama/llama-4-scout:free", "provider": "openrouter"},
    ]
    
    results = {}
    
    for config in test_configs:
        name = config["name"]
        model = config["model"]
        provider = config["provider"]
        
        print(f"\n  测试 {name}...")
        
        try:
            # 尝试导入和创建模型
            from src.utils.llm import get_model, ModelProvider
            
            llm = get_model(model, ModelProvider(provider))
            
            # 简单测试调用
            test_prompt = "Hello, this is a test. Please respond with 'OK'."
            response = llm.invoke(test_prompt)
            
            results[name] = {
                "success": True,
                "model": model,
                "provider": provider,
                "response_length": len(str(response.content)) if hasattr(response, 'content') else len(str(response))
            }
            
            print(f"    ✅ 连接成功")
            
        except Exception as e:
            results[name] = {
                "success": False,
                "model": model,
                "provider": provider,
                "error": str(e)[:200]
            }
            
            print(f"    ❌ 连接失败: {str(e)[:100]}...")
    
    return results


def check_data_availability() -> Dict[str, bool]:
    """检查NVDA数据可用性"""
    print("\n📊 检查NVDA数据可用性:")
    
    data_paths = {
        "本地新闻数据": "NVDA_alpha_news",
        "反思日志目录": "reasoning_logs/reflections",
        "实验日志目录": "reasoning_logs/experiment_2025-06-27",
        "错误日志目录": "reasoning_logs/errors"
    }
    
    results = {}
    
    for name, path in data_paths.items():
        exists = os.path.exists(path)
        results[name] = exists
        
        status = "✅" if exists else "❌"
        print(f"  {status} {name}: {path}")
        
        if exists and os.path.isdir(path):
            try:
                file_count = len(os.listdir(path))
                print(f"      包含 {file_count} 个文件/目录")
            except:
                pass
    
    return results


def analyze_recent_errors() -> List[Dict[str, any]]:
    """分析最近的错误"""
    print("\n🔍 分析最近的反思代理错误:")
    
    error_patterns = []
    
    # 检查反思日志中的错误模式
    reflection_dir = "reasoning_logs/reflections"
    if os.path.exists(reflection_dir):
        for file in os.listdir(reflection_dir):
            if file.endswith(".json"):
                try:
                    with open(os.path.join(reflection_dir, file), 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        
                    # 检查是否有错误模式
                    for ticker, reflection in data.get("reflections", {}).items():
                        if "Error occurred" in str(reflection.get("key_insights", [])):
                            error_patterns.append({
                                "file": file,
                                "ticker": ticker,
                                "error_type": "Default error response",
                                "reasoning": reflection.get("reasoning", "")
                            })
                            
                except Exception as e:
                    print(f"    ⚠️  无法读取文件 {file}: {e}")
    
    # 检查错误日志目录
    error_dir = "reasoning_logs/errors"
    if os.path.exists(error_dir):
        for file in os.listdir(error_dir):
            if "reflection" in file and file.endswith(".json"):
                try:
                    with open(os.path.join(error_dir, file), 'r', encoding='utf-8') as f:
                        error_data = json.load(f)
                        error_patterns.append({
                            "file": file,
                            "error_type": error_data.get("error_type", "Unknown"),
                            "error_message": error_data.get("error_message", "")[:100],
                            "model": error_data.get("model_name", "Unknown")
                        })
                except Exception as e:
                    print(f"    ⚠️  无法读取错误文件 {file}: {e}")
    
    if error_patterns:
        print(f"  发现 {len(error_patterns)} 个错误模式:")
        for i, pattern in enumerate(error_patterns[:5], 1):  # 只显示前5个
            print(f"    {i}. {pattern.get('error_type', 'Unknown')}")
            if 'error_message' in pattern:
                print(f"       消息: {pattern['error_message']}")
    else:
        print("  ✅ 未发现明显的错误模式")
    
    return error_patterns


def generate_recommendations(env_results: Dict[str, bool], 
                           connection_results: Dict[str, Dict[str, any]],
                           data_results: Dict[str, bool],
                           error_patterns: List[Dict[str, any]]) -> List[str]:
    """生成修复建议"""
    recommendations = []
    
    # 环境变量建议
    missing_keys = [k for k, v in env_results.items() if not v]
    if missing_keys:
        recommendations.append(f"配置缺失的API密钥: {', '.join(missing_keys)}")
    
    # 连接问题建议
    failed_connections = [name for name, result in connection_results.items() if not result["success"]]
    if failed_connections:
        recommendations.append(f"修复模型连接问题: {', '.join(failed_connections)}")
        recommendations.append("检查网络连接和API服务状态")
    
    # 数据可用性建议
    missing_data = [name for name, exists in data_results.items() if not exists]
    if missing_data:
        recommendations.append(f"创建缺失的数据目录: {', '.join(missing_data)}")
    
    # 错误模式建议
    if error_patterns:
        recommendations.append("查看详细错误日志以了解具体失败原因")
        recommendations.append("考虑使用备用模型或降低请求频率")
    
    # 通用建议
    recommendations.extend([
        "运行 test_reflection_agent.py 进行详细诊断",
        "检查系统时间和网络连接稳定性",
        "考虑增加重试次数和等待时间"
    ])
    
    return recommendations


def main():
    """主验证函数"""
    print("🔧 反思代理配置验证")
    print("=" * 50)
    
    # 执行各项检查
    env_results = check_environment_variables()
    connection_results = test_model_connections()
    data_results = check_data_availability()
    error_patterns = analyze_recent_errors()
    
    # 生成建议
    recommendations = generate_recommendations(env_results, connection_results, data_results, error_patterns)
    
    # 保存验证结果
    validation_results = {
        "timestamp": "2025-06-27T18:30:00",
        "environment_variables": env_results,
        "model_connections": connection_results,
        "data_availability": data_results,
        "error_patterns": error_patterns,
        "recommendations": recommendations
    }
    
    os.makedirs("reasoning_logs", exist_ok=True)
    with open("reasoning_logs/reflection_validation.json", 'w', encoding='utf-8') as f:
        json.dump(validation_results, f, indent=2, ensure_ascii=False)
    
    # 打印总结和建议
    print("\n" + "=" * 50)
    print("📋 验证总结:")
    print(f"环境变量配置: {sum(env_results.values())}/{len(env_results)} 正确")
    print(f"模型连接测试: {sum(1 for r in connection_results.values() if r['success'])}/{len(connection_results)} 成功")
    print(f"数据目录检查: {sum(data_results.values())}/{len(data_results)} 存在")
    print(f"发现错误模式: {len(error_patterns)} 个")
    
    print("\n💡 修复建议:")
    for i, rec in enumerate(recommendations, 1):
        print(f"{i}. {rec}")
    
    print(f"\n验证结果已保存到: reasoning_logs/reflection_validation.json")


if __name__ == "__main__":
    main()
