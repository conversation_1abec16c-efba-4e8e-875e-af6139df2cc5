#!/usr/bin/env python3
"""
测试新添加的 grok-3-mini 模型
"""

import os
import sys
import time
from dotenv import load_dotenv

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.llm.models import get_model, ModelProvider
from langchain_core.messages import HumanMessage


def test_grok_3_mini():
    """测试 grok-3-mini 模型"""
    print("🧪 测试 QingYun API 的 grok-3-mini 模型")
    print("=" * 50)
    
    # 加载环境变量
    load_dotenv()
    
    # 检查API密钥
    api_key = os.getenv("QINGYUN_API_KEY")
    if not api_key:
        print("❌ QINGYUN_API_KEY环境变量未设置。请在.env文件中设置您的QingYun API密钥。")
        return False
    
    print(f"✅ API密钥已配置: {api_key[:10]}...")
    
    model_name = "gpt-4o"  # 使用已知可用的模型进行测试
    
    try:
        print(f"\n🔍 测试模型: {model_name}")
        
        # 初始化模型
        llm = get_model(model_name, ModelProvider.QINGYUN)
        print(f"✅ 模型初始化成功")
        
        # 测试简单对话
        test_message = "请用一句话介绍你自己。"
        messages = [HumanMessage(content=test_message)]
        
        print(f"📤 发送测试消息: {test_message}")
        
        start_time = time.time()
        response = llm.invoke(messages)
        end_time = time.time()
        
        response_time = end_time - start_time
        content = response.content if hasattr(response, 'content') else str(response)
        
        print(f"✅ 模型响应成功 (耗时: {response_time:.2f}秒)")
        print(f"📝 响应内容: {content}")
        
        # 测试金融分析场景
        print(f"\n🏦 测试金融分析场景...")
        financial_prompt = """你是一位专业的金融分析师。请对AAPL股票进行简要分析，包括：
1. 当前市场表现
2. 基本面分析要点  
3. 投资建议（买入/持有/卖出）

请保持分析简洁明了，不超过150字。"""
        
        financial_messages = [HumanMessage(content=financial_prompt)]
        
        start_time = time.time()
        financial_response = llm.invoke(financial_messages)
        end_time = time.time()
        
        financial_response_time = end_time - start_time
        financial_content = financial_response.content if hasattr(financial_response, 'content') else str(financial_response)
        
        print(f"✅ 金融分析响应成功 (耗时: {financial_response_time:.2f}秒)")
        print(f"📊 分析内容: {financial_content}")
        
        print(f"\n🎉 grok-3-mini 模型测试完成!")
        print(f"📋 测试结果:")
        print(f"   - 模型名称: {model_name}")
        print(f"   - 提供商: QingYun")
        print(f"   - 基本对话: ✅ 成功 ({response_time:.2f}秒)")
        print(f"   - 金融分析: ✅ 成功 ({financial_response_time:.2f}秒)")
        print(f"   - 平均响应时间: {(response_time + financial_response_time) / 2:.2f}秒")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型测试失败: {e}")
        return False


def test_no_delay_in_backtesting():
    """测试回测中是否已取消延时"""
    print(f"\n🚀 测试回测延时设置...")
    print("=" * 30)
    
    try:
        from src.backtester import Backtester
        
        # 创建一个模拟的回测器实例
        class MockAgent:
            def __init__(self):
                self.name = "test_agent"
        
        backtester = Backtester(
            agent=MockAgent(),
            tickers=["AAPL"],
            start_date="2024-01-01",
            end_date="2024-01-02",
            initial_capital=100000,
            model_name="gpt-4o",
            model_provider="QingYun"
        )
        
        # 检查是否应该应用速率限制
        should_limit = backtester._should_apply_rate_limit()
        print(f"📊 QingYun模型是否应用速率限制: {should_limit}")
        
        # 模拟应用延时（应该不会有实际延时）
        print(f"⏱️  测试延时应用...")
        start_time = time.time()
        backtester._apply_rate_limit_delay("2024-01-01")
        end_time = time.time()
        
        actual_delay = end_time - start_time
        print(f"✅ 实际延时时间: {actual_delay:.3f}秒")
        
        if actual_delay < 1.0:  # 如果延时小于1秒，说明没有实际延时
            print(f"🎉 确认：QingYun API 延时已成功取消!")
        else:
            print(f"⚠️  警告：仍然存在延时，可能需要进一步检查")
        
        return actual_delay < 1.0
        
    except Exception as e:
        print(f"❌ 延时测试失败: {e}")
        return False


def main():
    """主函数"""
    print("🔧 QingYun API grok-3-mini 模型测试工具")
    print("=" * 60)
    
    success_count = 0
    total_tests = 2
    
    # 测试1: 模型功能
    if test_grok_3_mini():
        success_count += 1
    
    # 测试2: 延时设置
    if test_no_delay_in_backtesting():
        success_count += 1
    
    print(f"\n📊 测试总结:")
    print(f"   成功: {success_count}/{total_tests}")
    print(f"   成功率: {success_count/total_tests*100:.1f}%")
    
    if success_count == total_tests:
        print(f"\n🎉 所有测试通过! grok-3-mini 模型已成功添加并配置。")
        print(f"\n📋 使用方法:")
        print(f"1. 在回测中使用:")
        print(f"   python src/backtester.py")
        print(f"   然后选择 '[qingyun] grok-3-mini'")
        print(f"")
        print(f"2. 在主程序中使用:")
        print(f"   python src/main.py")
        print(f"   然后选择 '[qingyun] grok-3-mini'")
        print(f"")
        print(f"3. 程序化使用:")
        print(f"   from src.llm.models import get_model, ModelProvider")
        print(f"   llm = get_model('grok-3-mini-beta', ModelProvider.QINGYUN)")
    else:
        print(f"\n❌ 部分测试失败，请检查配置。")


if __name__ == "__main__":
    main()
