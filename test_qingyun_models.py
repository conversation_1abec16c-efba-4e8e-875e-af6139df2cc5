#!/usr/bin/env python3
"""
测试QingYun API可用的模型
"""

import json
import os
import sys
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.llm.models import get_model, ModelProvider
from langchain_core.messages import HumanMessage


def test_qingyun_models():
    """测试QingYun API的各种模型"""
    print("🧪 测试QingYun API可用模型")
    print("=" * 50)
    
    # QingYun API支持的模型列表
    qingyun_models = [
        "grok-beta",
        "claude-3-5-sonnet-20241022", 
        "gemini-2.0-flash-exp",
        "gpt-4o",
        "meta-llama/llama-4-scout",
        "meta-llama/llama-4-maverick"
    ]
    
    test_prompt = "请简单回答：什么是人工智能？"
    results = {}
    
    for model_name in qingyun_models:
        print(f"\n🔍 测试模型: {model_name}")
        
        try:
            llm = get_model(model_name, ModelProvider.QINGYUN)
            response = llm.invoke([HumanMessage(content=test_prompt)])
            
            results[model_name] = {
                "success": True,
                "response_length": len(response.content),
                "response_preview": response.content[:100] + "..." if len(response.content) > 100 else response.content
            }
            
            print(f"✅ 成功 - 响应长度: {len(response.content)} 字符")
            print(f"   预览: {response.content[:100]}...")
            
        except Exception as e:
            results[model_name] = {
                "success": False,
                "error": str(e)
            }
            
            print(f"❌ 失败 - {e}")
    
    # 总结结果
    print("\n📊 测试结果总结")
    print("=" * 30)
    
    working_models = []
    failed_models = []
    
    for model_name, result in results.items():
        if result["success"]:
            working_models.append(model_name)
            print(f"✅ {model_name}")
        else:
            failed_models.append(model_name)
            print(f"❌ {model_name}: {result['error']}")
    
    print(f"\n🎯 总结:")
    print(f"   可用模型: {len(working_models)} 个")
    print(f"   失败模型: {len(failed_models)} 个")
    
    if working_models:
        print(f"\n✅ 推荐使用的替代模型:")
        for model in working_models:
            if model != "grok-beta":  # 排除有问题的grok-beta
                print(f"   - {model}")
    
    return working_models, failed_models


def test_reflection_with_alternative_model():
    """使用替代模型测试反思分析"""
    print("\n🔄 使用替代模型测试反思分析")
    print("=" * 50)
    
    # 首先找到可用的模型
    working_models, _ = test_qingyun_models()
    
    if not working_models:
        print("❌ 没有可用的QingYun模型")
        return False
    
    # 选择一个替代模型（优先选择gpt-4o或claude）
    alternative_model = None
    preferred_models = ["gpt-4o", "claude-3-5-sonnet-20241022", "gemini-2.0-flash-exp"]
    
    for preferred in preferred_models:
        if preferred in working_models:
            alternative_model = preferred
            break
    
    if not alternative_model:
        alternative_model = working_models[0]  # 使用第一个可用的模型
    
    print(f"🎯 选择替代模型: {alternative_model}")
    
    # 导入反思分析函数
    from src.agents.reflection_analyst import generate_reflection_analysis
    
    # 创建简单测试数据
    test_decision = {
        "action": "hold",
        "quantity": 0,
        "confidence": 60.0,
        "reasoning": "Mixed signals from analysts, maintaining current position"
    }
    
    test_analyst_signals = {
        "fundamentals_analyst": {
            "signal": "bullish",
            "confidence": 80.0,
            "reasoning": "Strong financial metrics"
        },
        "market_analyst": {
            "signal": "bearish", 
            "confidence": 70.0,
            "reasoning": "Technical indicators suggest downside"
        }
    }
    
    try:
        print("🔍 调用反思分析...")
        result = generate_reflection_analysis(
            ticker="NVDA",
            decision=test_decision,
            analyst_signals=test_analyst_signals,
            model_name=alternative_model,
            model_provider="QingYun"
        )
        
        # 检查是否是默认错误响应
        if result.reasoning == "Error occurred during reflection analysis process, defaulting to fair evaluation":
            print("❌ 仍然出现默认错误响应")
            return False
        else:
            print("✅ 反思分析成功")
            print(f"   决策质量: {result.decision_quality}")
            print(f"   正确性评分: {result.correctness_score}")
            print(f"   推理长度: {len(result.reasoning)} 字符")
            return True
            
    except Exception as e:
        print(f"❌ 反思分析失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 QingYun API模型可用性测试")
    print(f"⏰ 测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 检查环境变量
    if not os.getenv("QINGYUN_API_KEY"):
        print("❌ QINGYUN_API_KEY环境变量未设置")
        return
    
    # 测试1: 检查可用模型
    working_models, failed_models = test_qingyun_models()
    
    # 测试2: 使用替代模型测试反思分析
    if working_models:
        reflection_success = test_reflection_with_alternative_model()
    else:
        reflection_success = False
    
    # 总结
    print(f"\n🎯 最终结论:")
    if "grok-beta" in failed_models:
        print("❌ grok-beta模型在QingYun API中不可用")
        print("   错误原因: 服务器端未配置该模型")
    
    if working_models:
        print(f"✅ 发现 {len(working_models)} 个可用的替代模型")
        print("💡 建议: 使用以下模型替代grok-beta:")
        for model in working_models[:3]:  # 显示前3个
            print(f"   - {model}")
    
    if reflection_success:
        print("✅ 反思分析功能在替代模型上工作正常")
    else:
        print("❌ 反思分析功能需要进一步调试")


if __name__ == "__main__":
    main()
