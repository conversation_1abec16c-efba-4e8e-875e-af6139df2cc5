#!/usr/bin/env python3
"""
最终配置测试：验证 grok-3-mini 模型添加和 QingYun API 延时取消
"""

import os
import sys
import time
from dotenv import load_dotenv

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.llm.models import get_model, ModelProvider
from langchain_core.messages import HumanMessage


def test_qingyun_no_delay():
    """测试 QingYun API 延时取消功能"""
    print("🚀 测试 QingYun API 延时取消功能")
    print("=" * 40)
    
    try:
        from src.backtester import Backtester
        
        # 创建一个模拟的回测器实例
        class MockAgent:
            def __init__(self):
                self.name = "test_agent"
        
        backtester = Backtester(
            agent=MockAgent(),
            tickers=["AAPL"],
            start_date="2024-01-01",
            end_date="2024-01-02",
            initial_capital=100000,
            model_name="grok-beta",
            model_provider="QingYun"
        )
        
        # 检查是否应该应用速率限制
        should_limit = backtester._should_apply_rate_limit()
        print(f"📊 QingYun模型是否检测到需要速率限制: {should_limit}")
        
        # 测试延时应用（应该不会有实际延时）
        print(f"⏱️  测试延时应用...")
        start_time = time.time()
        backtester._apply_rate_limit_delay("2024-01-01")
        end_time = time.time()
        
        actual_delay = end_time - start_time
        print(f"✅ 实际延时时间: {actual_delay:.3f}秒")
        
        if actual_delay < 1.0:  # 如果延时小于1秒，说明没有实际延时
            print(f"🎉 确认：QingYun API 延时已成功取消!")
            return True
        else:
            print(f"⚠️  警告：仍然存在延时，可能需要进一步检查")
            return False
        
    except Exception as e:
        print(f"❌ 延时测试失败: {e}")
        return False


def test_grok_3_mini_placeholder():
    """测试 grok-3-mini 占位符配置"""
    print("\n🧪 测试 grok-3-mini 占位符配置")
    print("=" * 40)
    
    # 加载环境变量
    load_dotenv()
    
    # 检查API密钥
    api_key = os.getenv("QINGYUN_API_KEY")
    if not api_key:
        print("❌ QINGYUN_API_KEY环境变量未设置。")
        return False
    
    print(f"✅ API密钥已配置")
    
    try:
        # 测试占位符模型（实际使用 grok-beta）
        llm = get_model("grok-beta", ModelProvider.QINGYUN)
        print(f"✅ grok-3-mini 占位符模型初始化成功（实际使用 grok-beta）")
        
        # 测试简单对话
        test_message = "请用一句话介绍你自己。"
        messages = [HumanMessage(content=test_message)]
        
        print(f"📤 发送测试消息: {test_message}")
        
        start_time = time.time()
        response = llm.invoke(messages)
        end_time = time.time()
        
        response_time = end_time - start_time
        content = response.content if hasattr(response, 'content') else str(response)
        
        print(f"✅ 模型响应成功 (耗时: {response_time:.2f}秒)")
        print(f"📝 响应内容: {content[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型测试失败: {e}")
        return False


def check_api_models_json():
    """检查 API 模型配置文件"""
    print("\n📋 检查 API 模型配置文件")
    print("=" * 30)
    
    try:
        import json
        
        with open("src/llm/api_models.json", "r", encoding="utf-8") as f:
            models = json.load(f)
        
        # 查找 grok-3-mini 相关配置
        grok_3_mini_found = False
        for model in models:
            if "grok-3-mini" in model.get("display_name", ""):
                grok_3_mini_found = True
                print(f"✅ 找到 grok-3-mini 配置:")
                print(f"   显示名称: {model['display_name']}")
                print(f"   模型名称: {model['model_name']}")
                print(f"   提供商: {model['provider']}")
                break
        
        if not grok_3_mini_found:
            print(f"❌ 未找到 grok-3-mini 配置")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 配置文件检查失败: {e}")
        return False


def main():
    """主函数"""
    print("🔧 QingYun API 配置最终测试")
    print("=" * 60)
    
    success_count = 0
    total_tests = 3
    
    # 测试1: 延时取消功能
    if test_qingyun_no_delay():
        success_count += 1
    
    # 测试2: grok-3-mini 占位符
    if test_grok_3_mini_placeholder():
        success_count += 1
    
    # 测试3: 配置文件检查
    if check_api_models_json():
        success_count += 1
    
    print(f"\n📊 测试总结:")
    print(f"   成功: {success_count}/{total_tests}")
    print(f"   成功率: {success_count/total_tests*100:.1f}%")
    
    if success_count == total_tests:
        print(f"\n🎉 所有测试通过! 配置已成功完成。")
        print(f"\n📋 配置总结:")
        print(f"✅ 已添加 grok-3-mini 模型配置（当前使用 grok-beta 作为占位符）")
        print(f"✅ 已取消 QingYun API 的延时限制")
        print(f"\n📋 使用方法:")
        print(f"1. 在回测中使用:")
        print(f"   python src/backtester.py")
        print(f"   然后选择 '[qingyun] grok-3-mini (placeholder)'")
        print(f"")
        print(f"2. 在主程序中使用:")
        print(f"   python src/main.py")
        print(f"   然后选择 '[qingyun] grok-3-mini (placeholder)'")
        print(f"")
        print(f"📝 注意事项:")
        print(f"- 当前 grok-3-mini 在 QingYun API 中不可用")
        print(f"- 占位符配置使用 grok-beta 模型")
        print(f"- 一旦 grok-3-mini 可用，只需更新 model_name 即可")
    else:
        print(f"\n❌ 部分测试失败，请检查配置。")


if __name__ == "__main__":
    main()
