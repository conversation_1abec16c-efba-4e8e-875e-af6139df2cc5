#!/usr/bin/env python3
"""
测试修复后的反思分析师
"""

import json
import os
import sys
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.agents.reflection_analyst import generate_reflection_analysis


def load_real_backtest_data():
    """加载真实的回测数据"""
    print("📂 加载真实回测数据...")
    
    # 加载投资组合决策
    portfolio_file = "reasoning_logs/experiment_2025-06-27/2025-01-01/2025-01-01_NVDA_portfolio_manager_agent_181648221.json"
    with open(portfolio_file, 'r', encoding='utf-8') as f:
        portfolio_data = json.load(f)
    
    decision = portfolio_data["reasoning"]
    print(f"✅ 投资组合决策加载成功")
    
    # 加载分析师信号（只加载几个主要的，避免数据过大）
    input_data_dir = "reasoning_logs/experiment_2025-06-27/2025-01-01/input_data"
    analyst_signals = {}
    
    # 只加载关键分析师，减少数据大小
    key_analysts = [
        "fundamentals_analyst_agent",
        "market_analyst_agent", 
        "news_analyst_agent",
        "aswath_damodaran_agent",
        "warren_buffett_agent"
    ]
    
    for analyst in key_analysts:
        file_path = f"{input_data_dir}/{analyst}_NVDA_2025-01-01.json"
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                analyst_signals[analyst] = data
    
    print(f"✅ 分析师信号加载成功，共 {len(analyst_signals)} 个分析师")
    
    return decision, analyst_signals


def test_fixed_reflection_with_grok():
    """测试修复后的反思分析师使用grok模型"""
    print("🔧 测试修复后的反思分析师（grok模型）")
    print("=" * 50)
    
    # 加载真实数据
    decision, analyst_signals = load_real_backtest_data()
    
    model_name = "grok-beta"
    model_provider = "QingYun"
    
    try:
        print("1️⃣ 调用修复后的反思分析函数...")
        print(f"   决策数据大小: {len(json.dumps(decision))} 字符")
        print(f"   分析师信号数据大小: {len(json.dumps(analyst_signals))} 字符")
        
        result = generate_reflection_analysis(
            ticker="NVDA",
            decision=decision,
            analyst_signals=analyst_signals,
            model_name=model_name,
            model_provider=model_provider
        )
        
        print(f"✅ 反思分析调用成功")
        print(f"   结果类型: {type(result)}")
        print(f"   决策质量: {result.decision_quality}")
        print(f"   正确性评分: {result.correctness_score}")
        print(f"   关键洞察数量: {len(result.key_insights)}")
        print(f"   推荐建议数量: {len(result.recommendations)}")
        print(f"   推理过程长度: {len(result.reasoning)} 字符")
        
        # 检查是否是默认错误响应
        if "Error occurred during reflection analysis process, defaulting to fair evaluation" in result.reasoning:
            print("❌ 仍然出现默认错误响应")
            print(f"   错误详情: {result.reasoning}")
            return False
        elif "All fallback models failed" in result.reasoning:
            print("❌ 所有回退模型都失败了")
            print(f"   错误详情: {result.reasoning}")
            return False
        else:
            print("✅ 获得了有效的分析结果")
            
            # 显示详细结果
            print("\n📋 详细分析结果:")
            print(f"决策质量: {result.decision_quality}")
            print(f"正确性评分: {result.correctness_score}")
            print("\n关键洞察:")
            for i, insight in enumerate(result.key_insights, 1):
                print(f"  {i}. {insight}")
            print("\n推荐建议:")
            for i, rec in enumerate(result.recommendations, 1):
                print(f"  {i}. {rec}")
            print(f"\n推理过程:\n{result.reasoning}")
            
            return True
            
    except Exception as e:
        print(f"❌ 反思分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_simple_data_with_grok():
    """使用简单数据测试grok模型"""
    print("\n🧪 使用简单数据测试grok模型")
    print("=" * 50)
    
    # 创建简单测试数据
    simple_decision = {
        "action": "hold",
        "quantity": 0,
        "confidence": 60.0,
        "reasoning": "Mixed signals from analysts"
    }
    
    simple_signals = {
        "fundamentals_analyst": {
            "signal": "bullish",
            "confidence": 80.0,
            "reasoning": "Strong fundamentals"
        },
        "market_analyst": {
            "signal": "bearish", 
            "confidence": 70.0,
            "reasoning": "Technical indicators suggest downside"
        }
    }
    
    model_name = "grok-beta"
    model_provider = "QingYun"
    
    try:
        print("1️⃣ 调用反思分析函数（简单数据）...")
        
        result = generate_reflection_analysis(
            ticker="NVDA",
            decision=simple_decision,
            analyst_signals=simple_signals,
            model_name=model_name,
            model_provider=model_provider
        )
        
        print(f"✅ 反思分析调用成功")
        print(f"   决策质量: {result.decision_quality}")
        print(f"   正确性评分: {result.correctness_score}")
        print(f"   推理过程长度: {len(result.reasoning)} 字符")
        
        # 检查结果质量
        if "Error occurred during reflection analysis process" in result.reasoning:
            print("❌ 出现默认错误响应")
            return False
        elif "All fallback models failed" in result.reasoning:
            print("❌ 所有回退模型都失败了")
            return False
        else:
            print("✅ 获得了有效的分析结果")
            return True
            
    except Exception as e:
        print(f"❌ 反思分析失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 测试修复后的反思分析师")
    print(f"⏰ 测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 检查环境变量
    if not os.getenv("QINGYUN_API_KEY"):
        print("❌ QINGYUN_API_KEY环境变量未设置")
        return
    
    test_results = {}
    
    # 测试1: 简单数据
    test_results["simple_data"] = test_simple_data_with_grok()
    
    # 测试2: 真实数据（如果简单数据成功）
    if test_results["simple_data"]:
        test_results["real_data"] = test_fixed_reflection_with_grok()
    else:
        print("⚠️ 跳过真实数据测试，因为简单数据测试失败")
        test_results["real_data"] = False
    
    # 总结结果
    print("\n📊 最终测试结果总结")
    print("=" * 30)
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    overall_success = all(test_results.values())
    if overall_success:
        print(f"\n🎯 总体结果: ✅ 所有测试通过")
        print("💡 修复成功！反思分析师现在可以正常工作了")
    else:
        print(f"\n🎯 总体结果: ❌ 存在失败的测试")
        print("💡 建议：检查模型可用性或考虑使用其他模型")


if __name__ == "__main__":
    main()
