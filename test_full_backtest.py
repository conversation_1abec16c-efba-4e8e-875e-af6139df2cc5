#!/usr/bin/env python3
"""
自动化测试完整回测流程，验证反思分析师在处理所有分析师时的表现
"""

import os
import sys
import json
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.graph.state import AgentState
from src.utils.analysts import ANALYST_CONFIG
from src.agents.reflection_analyst import reflection_analyst_agent


def create_mock_state_with_all_analysts():
    """创建包含所有分析师信号的模拟状态"""
    
    # 基础数据
    base_data = {
        "tickers": ["NVDA"],
        "end_date": "2025-01-01",
        "analyst_signals": {}
    }
    
    # 为每个分析师创建模拟信号
    for analyst_key, config in ANALYST_CONFIG.items():
        if analyst_key != "reflection_analyst":  # 排除反思分析师自己
            base_data["analyst_signals"][analyst_key] = {
                "NVDA": {
                    "signal": "buy" if hash(analyst_key) % 3 == 0 else ("sell" if hash(analyst_key) % 3 == 1 else "hold"),
                    "confidence": 0.6 + (hash(analyst_key) % 40) / 100,
                    "reasoning": f"""
                    {config['display_name']}的详细分析：
                    
                    基于我的投资理念和分析方法，对NVDA进行了全面评估：
                    
                    1. 基本面分析：
                       - 营收增长强劲，AI芯片需求旺盛
                       - 毛利率保持高位，显示定价能力强
                       - 现金流充沛，财务状况健康
                       - 研发投入持续增加，技术领先优势明显
                    
                    2. 竞争优势分析：
                       - 在AI芯片领域具有技术护城河
                       - 生态系统完整，客户粘性高
                       - 品牌价值和市场地位稳固
                       - 供应链管理能力强
                    
                    3. 市场前景：
                       - AI应用快速普及，市场空间巨大
                       - 数据中心升级需求持续
                       - 自动驾驶等新兴应用带来增长机会
                       - 地缘政治因素需要关注
                    
                    4. 风险评估：
                       - 估值较高，存在回调风险
                       - 行业竞争加剧的可能性
                       - 宏观经济波动的影响
                       - 监管政策变化的不确定性
                    
                    5. 投资建议：
                       基于以上分析，我认为NVDA{
                           '具有长期投资价值，建议买入' if hash(analyst_key) % 3 == 0 else
                           '估值过高，建议卖出' if hash(analyst_key) % 3 == 1 else
                           '可以持有观望，等待更好时机'
                       }。
                    
                    关键指标：
                    - 目标价格：${700 + hash(analyst_key) % 300}
                    - 风险评级：{'中等' if hash(analyst_key) % 2 == 0 else '较高'}
                    - 投资期限：{'长期' if hash(analyst_key) % 3 == 0 else '中期'}
                    """,
                    "key_metrics": {
                        "pe_ratio": 30 + hash(analyst_key) % 25,
                        "growth_rate": 20 + hash(analyst_key) % 30,
                        "risk_score": 0.3 + (hash(analyst_key) % 40) / 100
                    },
                    "timestamp": datetime.now().isoformat()
                }
            }
    
    # 创建投资组合管理决策
    portfolio_decision = {
        "NVDA": {
            "action": "buy",
            "shares": 100,
            "confidence": 0.75,
            "reasoning": """
            综合所有分析师的建议，决定买入NVDA：
            
            1. 多数分析师看好长期前景
            2. 基本面强劲，技术优势明显
            3. 市场需求持续增长
            4. 风险可控，收益预期较高
            
            分配适中仓位，平衡收益与风险。
            """,
            "risk_assessment": "medium",
            "expected_return": 0.20
        }
    }
    
    # 创建模拟消息
    from langchain_core.messages import HumanMessage
    portfolio_message = HumanMessage(
        content=json.dumps(portfolio_decision),
        name="portfolio_manager"
    )
    
    # 创建状态
    state = AgentState(
        messages=[portfolio_message],
        data=base_data,
        metadata={
            "model_name": "grok-beta",
            "model_provider": "QingYun",
            "show_reasoning": True
        }
    )
    
    return state


def test_reflection_with_all_analysts():
    """测试反思分析师处理所有分析师数据"""
    print("🧪 测试反思分析师处理所有分析师数据...")
    
    # 创建包含所有分析师的状态
    state = create_mock_state_with_all_analysts()
    
    print(f"   分析师数量: {len(state['data']['analyst_signals'])}")
    
    # 计算数据大小
    total_chars = 0
    for analyst_signals in state['data']['analyst_signals'].values():
        total_chars += len(json.dumps(analyst_signals, ensure_ascii=False))
    
    print(f"   总数据大小: {total_chars:,} 字符 ({total_chars/1024/1024:.2f} MB)")
    
    try:
        # 运行反思分析师
        result = reflection_analyst_agent(state)
        
        # 检查结果
        if "messages" in result and len(result["messages"]) > len(state["messages"]):
            reflection_message = result["messages"][-1]
            reflection_data = json.loads(reflection_message.content)
            
            print("✅ 反思分析成功完成")
            
            for ticker, analysis in reflection_data.items():
                print(f"\n   📊 {ticker} 分析结果:")
                print(f"      决策质量: {analysis['decision_quality']}")
                print(f"      正确性评分: {analysis['correctness_score']}")
                print(f"      关键洞察数量: {len(analysis['key_insights'])}")
                print(f"      推荐建议数量: {len(analysis['recommendations'])}")
                print(f"      推理长度: {len(analysis['reasoning'])} 字符")
                
                # 检查是否是错误的默认响应
                if "Error occurred during reflection analysis process" in analysis['reasoning']:
                    print("      ⚠️  检测到默认错误响应")
                    return False
                else:
                    print("      ✅ 生成了有效的分析内容")
            
            return True
        else:
            print("❌ 反思分析师没有生成输出")
            return False
            
    except Exception as e:
        print(f"❌ 反思分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_data_size_threshold():
    """测试不同数据大小下的表现"""
    print("\n🧪 测试不同数据大小下的表现...")
    
    # 测试不同数量的分析师
    analyst_counts = [5, 10, 15, 20]  # 对应不同的数据量
    
    for count in analyst_counts:
        print(f"\n   测试 {count} 个分析师...")
        
        # 创建有限数量分析师的状态
        state = create_mock_state_with_all_analysts()
        
        # 只保留前N个分析师
        all_analysts = list(state['data']['analyst_signals'].keys())
        limited_analysts = {k: v for k, v in state['data']['analyst_signals'].items() 
                          if k in all_analysts[:count]}
        state['data']['analyst_signals'] = limited_analysts
        
        # 计算数据大小
        total_chars = sum(len(json.dumps(signals, ensure_ascii=False)) 
                         for signals in limited_analysts.values())
        
        print(f"      数据大小: {total_chars:,} 字符")
        
        try:
            result = reflection_analyst_agent(state)
            
            if "messages" in result and len(result["messages"]) > len(state["messages"]):
                reflection_message = result["messages"][-1]
                reflection_data = json.loads(reflection_message.content)
                
                analysis = reflection_data.get("NVDA", {})
                if "Error occurred during reflection analysis process" not in analysis.get('reasoning', ''):
                    print(f"      ✅ 成功 - 质量: {analysis.get('decision_quality', 'N/A')}")
                else:
                    print(f"      ❌ 默认响应")
            else:
                print(f"      ❌ 无输出")
                
        except Exception as e:
            print(f"      ❌ 异常: {e}")


def main():
    """主测试函数"""
    print("🚀 开始测试反思分析师处理所有分析师数据的完整流程\n")
    
    # 运行测试
    tests = [
        ("所有分析师反思分析", test_reflection_with_all_analysts),
        ("数据大小阈值测试", test_data_size_threshold)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            if test_name == "数据大小阈值测试":
                test_func()  # 这个测试不返回布尔值
                results.append((test_name, True))
            else:
                success = test_func()
                results.append((test_name, success))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "="*60)
    print("📊 测试结果总结:")
    print("="*60)
    
    passed = 0
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"   {test_name}: {status}")
        if success:
            passed += 1
    
    print(f"\n总体结果: {passed}/{len(results)} 测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！反思分析师已准备好处理完整的分析师数据集。")
        print("\n💡 建议:")
        print("   - 系统现在可以稳定处理所有分析师的数据")
        print("   - 大数据量时会自动使用分块策略")
        print("   - 具备多模型回退机制，确保可靠性")
        print("   - 可以安全地运行包含所有分析师的完整回测")
    else:
        print("⚠️  部分测试失败，需要进一步调试。")
    
    return passed == len(results)


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
