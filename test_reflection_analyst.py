#!/usr/bin/env python3
"""
Test script to validate reflection analyst with grok-beta model
"""

import json
import sys
import os
from datetime import datetime

# Add src to path
sys.path.append('src')

from src.agents.reflection_analyst import generate_reflection_analysis, ReflectionAnalysis

def create_mock_analyst_signals():
    """Create a comprehensive set of mock analyst signals to test chunking"""
    return {
        "fundamentals_analyst": {
            "signal": "bullish",
            "confidence": 85,
            "reasoning": "Strong revenue growth and improving margins indicate solid fundamentals"
        },
        "valuation_agent": {
            "signal": "bearish", 
            "confidence": 75,
            "reasoning": "Current P/E ratio is significantly above historical averages"
        },
        "technical_analyst": {
            "signal": "neutral",
            "confidence": 60,
            "reasoning": "Mixed technical indicators with support at current levels"
        },
        "news_analyst": {
            "signal": "bullish",
            "confidence": 70,
            "reasoning": "Recent positive earnings report and product announcements"
        },
        "social_media_analyst": {
            "signal": "bearish",
            "confidence": 65,
            "reasoning": "Negative sentiment trending on social media platforms"
        },
        "buffett_analyst": {
            "signal": "bullish",
            "confidence": 80,
            "reasoning": "Strong competitive moat and consistent cash flow generation"
        },
        "graham_analyst": {
            "signal": "neutral",
            "confidence": 55,
            "reasoning": "Meets some value criteria but price is not at significant discount"
        },
        "lynch_analyst": {
            "signal": "bullish",
            "confidence": 75,
            "reasoning": "Growth at reasonable price with strong market position"
        },
        "munger_analyst": {
            "signal": "bullish",
            "confidence": 85,
            "reasoning": "Excellent management team and sustainable business model"
        },
        "burry_analyst": {
            "signal": "bearish",
            "confidence": 90,
            "reasoning": "Significant overvaluation based on contrarian analysis"
        },
        "ackman_analyst": {
            "signal": "neutral",
            "confidence": 60,
            "reasoning": "Activist opportunities limited but business quality is decent"
        },
        "wood_analyst": {
            "signal": "bullish",
            "confidence": 95,
            "reasoning": "Disruptive innovation potential with exponential growth trajectory"
        },
        "fisher_analyst": {
            "signal": "bullish",
            "confidence": 80,
            "reasoning": "Superior management and strong research & development capabilities"
        },
        "damodaran_analyst": {
            "signal": "bearish",
            "confidence": 70,
            "reasoning": "Intrinsic value calculation suggests current price is too high"
        },
        "druckenmiller_analyst": {
            "signal": "neutral",
            "confidence": 65,
            "reasoning": "Macro environment creates uncertainty for near-term performance"
        }
    }

def test_reflection_analyst_chunking():
    """Test reflection analyst with large payload that should trigger chunking"""
    print("Testing reflection analyst with large payload (chunking scenario)...")
    
    # Create mock decision
    mock_decision = {
        "action": "hold",
        "quantity": 0,
        "confidence": 0.65,
        "reasoning": "Mixed analyst signals with 8 bullish, 4 bearish, and 3 neutral recommendations"
    }
    
    # Create comprehensive analyst signals
    analyst_signals = create_mock_analyst_signals()
    
    print(f"Created {len(analyst_signals)} analyst signals for testing")
    
    # Calculate approximate payload size
    payload_size = len(json.dumps(analyst_signals, indent=2))
    print(f"Approximate payload size: {payload_size} characters")
    
    try:
        # Test with grok-beta model (should trigger chunking due to size limits)
        result = generate_reflection_analysis(
            ticker="NVDA",
            decision=mock_decision,
            analyst_signals=analyst_signals,
            model_name="grok-beta",
            model_provider="QingYun"
        )
        
        print(f"Reflection analysis completed successfully!")
        print(f"Result type: {type(result)}")
        
        if isinstance(result, ReflectionAnalysis):
            print(f"Decision quality: {result.decision_quality}")
            print(f"Correctness score: {result.correctness_score}")
            print(f"Number of insights: {len(result.key_insights)}")
            print(f"Number of recommendations: {len(result.recommendations)}")
            print(f"Reasoning length: {len(result.reasoning)} characters")
            return True
        else:
            print(f"Unexpected result type: {type(result)}")
            return False
            
    except Exception as e:
        print(f"Reflection analysis failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_reflection_analyst_small_payload():
    """Test reflection analyst with small payload (no chunking)"""
    print("\nTesting reflection analyst with small payload (no chunking scenario)...")
    
    # Create mock decision
    mock_decision = {
        "action": "buy",
        "quantity": 100,
        "confidence": 0.8,
        "reasoning": "Strong bullish signals from key analysts"
    }
    
    # Create small set of analyst signals
    small_analyst_signals = {
        "fundamentals_analyst": {
            "signal": "bullish",
            "confidence": 85,
            "reasoning": "Strong fundamentals"
        },
        "technical_analyst": {
            "signal": "bullish", 
            "confidence": 75,
            "reasoning": "Positive technical indicators"
        },
        "news_analyst": {
            "signal": "bullish",
            "confidence": 80,
            "reasoning": "Positive news sentiment"
        }
    }
    
    payload_size = len(json.dumps(small_analyst_signals, indent=2))
    print(f"Small payload size: {payload_size} characters")
    
    try:
        result = generate_reflection_analysis(
            ticker="NVDA",
            decision=mock_decision,
            analyst_signals=small_analyst_signals,
            model_name="grok-beta",
            model_provider="QingYun"
        )
        
        print(f"Small payload reflection analysis completed!")
        print(f"Result type: {type(result)}")
        
        if isinstance(result, ReflectionAnalysis):
            print(f"Decision quality: {result.decision_quality}")
            print(f"Correctness score: {result.correctness_score}")
            return True
        else:
            print(f"Unexpected result type: {type(result)}")
            return False
            
    except Exception as e:
        print(f"Small payload reflection analysis failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run reflection analyst tests"""
    print("=" * 70)
    print("Testing Reflection Analyst with grok-beta model fixes")
    print("=" * 70)
    
    tests = [
        ("Large Payload (Chunking)", test_reflection_analyst_chunking),
        ("Small Payload (No Chunking)", test_reflection_analyst_small_payload)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"✅ {test_name}: {'PASSED' if result else 'FAILED'}")
        except Exception as e:
            results.append((test_name, False))
            print(f"❌ {test_name}: FAILED - {e}")
        print("-" * 50)
    
    # Summary
    print("\n" + "=" * 70)
    print("TEST SUMMARY")
    print("=" * 70)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All reflection analyst tests passed!")
        print("The grok-beta model fixes are working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. The fixes may need additional work.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
