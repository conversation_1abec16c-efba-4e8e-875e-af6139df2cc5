#!/usr/bin/env python3
"""
测试反思分析师在处理所有分析师数据时的性能
验证数据分块策略和回退机制是否有效
"""

import json
import os
import sys
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.agents.reflection_analyst import generate_reflection_analysis, generate_chunked_reflection_analysis


def create_large_analyst_signals():
    """创建模拟的大量分析师信号数据"""
    
    # 所有可能的分析师类型
    all_analysts = [
        "aswath_damodaran_agent", "ben_graham_agent", "bill_ackman_agent", 
        "cathie_wood_agent", "charlie_munger_agent", "michael_burry_agent",
        "phil_fisher_agent", "peter_lynch_agent", "stanley_druckenmiller_agent",
        "warren_buffett_agent", "fundamentals_agent", "technical_analyst_agent",
        "valuation_agent", "sentiment_agent", "factual_news_agent", 
        "subjective_news_agent", "market_analyst_agent", "news_analyst_agent",
        "social_media_analyst_agent", "fundamentals_analyst_agent"
    ]
    
    # 为每个分析师创建详细的信号数据
    analyst_signals = {}
    
    for analyst in all_analysts:
        analyst_signals[analyst] = {
            "signal": "buy" if hash(analyst) % 3 == 0 else ("sell" if hash(analyst) % 3 == 1 else "hold"),
            "confidence": 0.7 + (hash(analyst) % 30) / 100,
            "reasoning": f"""
            基于{analyst}的分析方法，我们对NVDA进行了深入分析：
            
            1. 财务指标分析：
               - 营收增长率：{85 + hash(analyst) % 20}%
               - 毛利率：{70 + hash(analyst) % 15}%
               - 净利润率：{25 + hash(analyst) % 10}%
               - ROE：{30 + hash(analyst) % 15}%
               - 债务股权比：{0.1 + (hash(analyst) % 20) / 100}
            
            2. 市场表现分析：
               - 相对强弱指数：{60 + hash(analyst) % 30}
               - 移动平均线趋势：{'上升' if hash(analyst) % 2 == 0 else '下降'}
               - 成交量分析：{'放量' if hash(analyst) % 3 == 0 else '缩量'}
               - 技术指标汇总：{'看涨' if hash(analyst) % 2 == 0 else '看跌'}
            
            3. 行业前景分析：
               - AI芯片市场规模预期增长：{150 + hash(analyst) % 50}%
               - 竞争地位：{'领先' if hash(analyst) % 2 == 0 else '稳固'}
               - 技术护城河：{'很强' if hash(analyst) % 3 == 0 else '较强'}
               - 市场份额：{75 + hash(analyst) % 20}%
            
            4. 风险因素评估：
               - 地缘政治风险：{'中等' if hash(analyst) % 2 == 0 else '较高'}
               - 监管风险：{'低' if hash(analyst) % 3 == 0 else '中等'}
               - 技术替代风险：{'低' if hash(analyst) % 2 == 0 else '中等'}
               - 供应链风险：{'可控' if hash(analyst) % 3 == 0 else '需关注'}
            
            5. 估值分析：
               - P/E比率：{35 + hash(analyst) % 20}
               - P/B比率：{8 + hash(analyst) % 10}
               - PEG比率：{1.2 + (hash(analyst) % 30) / 100}
               - 目标价格：${800 + hash(analyst) % 200}
            
            综合以上分析，{analyst}认为NVDA当前{
                '被低估，建议买入' if hash(analyst) % 3 == 0 else 
                '被高估，建议卖出' if hash(analyst) % 3 == 1 else 
                '估值合理，建议持有'
            }。
            
            关键催化剂：
            - 新产品发布周期
            - 数据中心需求增长
            - AI应用普及速度
            - 竞争对手动态
            
            主要风险：
            - 宏观经济波动
            - 行业周期性调整
            - 技术发展不确定性
            - 监管政策变化
            """,
            "key_metrics": {
                "revenue_growth": 85 + hash(analyst) % 20,
                "profit_margin": 25 + hash(analyst) % 10,
                "pe_ratio": 35 + hash(analyst) % 20,
                "technical_score": 60 + hash(analyst) % 30,
                "sentiment_score": 0.6 + (hash(analyst) % 40) / 100
            },
            "risk_assessment": {
                "overall_risk": "medium" if hash(analyst) % 2 == 0 else "high",
                "volatility": 0.3 + (hash(analyst) % 20) / 100,
                "liquidity_risk": "low" if hash(analyst) % 3 == 0 else "medium"
            },
            "timestamp": datetime.now().isoformat(),
            "data_sources": [
                "financial_statements", "market_data", "news_analysis", 
                "technical_indicators", "industry_reports"
            ]
        }
    
    return analyst_signals


def create_portfolio_decision():
    """创建模拟的投资组合决策"""
    return {
        "action": "buy",
        "shares": 150,
        "confidence": 0.75,
        "reasoning": """
        基于综合分析，决定买入NVDA 150股：
        
        1. 多数分析师看好NVDA长期前景
        2. AI芯片市场持续增长
        3. 公司财务状况良好
        4. 技术指标显示上升趋势
        5. 风险可控，收益预期较高
        
        分配75%的最大仓位，体现了对该投资的信心。
        """,
        "risk_factors": [
            "市场波动风险",
            "行业竞争加剧",
            "监管政策变化"
        ],
        "expected_return": 0.25,
        "max_loss": 0.15,
        "time_horizon": "6-12个月"
    }


def test_normal_reflection():
    """测试正常的反思分析"""
    print("🧪 测试正常反思分析...")
    
    decision = create_portfolio_decision()
    # 创建较小的信号数据集
    small_signals = {k: v for i, (k, v) in enumerate(create_large_analyst_signals().items()) if i < 5}
    
    try:
        result = generate_reflection_analysis(
            ticker="NVDA",
            decision=decision,
            analyst_signals=small_signals,
            model_name="grok-beta",
            model_provider="QingYun"
        )
        
        print(f"✅ 正常分析成功")
        print(f"   决策质量: {result.decision_quality}")
        print(f"   正确性评分: {result.correctness_score}")
        print(f"   关键洞察数量: {len(result.key_insights)}")
        print(f"   推荐建议数量: {len(result.recommendations)}")
        return True
        
    except Exception as e:
        print(f"❌ 正常分析失败: {e}")
        return False


def test_large_data_reflection():
    """测试大数据量的反思分析"""
    print("\n🧪 测试大数据量反思分析...")
    
    decision = create_portfolio_decision()
    large_signals = create_large_analyst_signals()
    
    # 计算数据大小
    decision_str = json.dumps(decision, indent=2, ensure_ascii=False)
    signals_str = json.dumps(large_signals, indent=2, ensure_ascii=False)
    total_size = len(decision_str) + len(signals_str)
    
    print(f"   数据大小: {total_size:,} 字符 ({total_size/1024/1024:.2f} MB)")
    print(f"   分析师数量: {len(large_signals)}")
    
    try:
        result = generate_reflection_analysis(
            ticker="NVDA",
            decision=decision,
            analyst_signals=large_signals,
            model_name="grok-beta",
            model_provider="QingYun"
        )
        
        print(f"✅ 大数据分析成功")
        print(f"   决策质量: {result.decision_quality}")
        print(f"   正确性评分: {result.correctness_score}")
        print(f"   关键洞察数量: {len(result.key_insights)}")
        print(f"   推荐建议数量: {len(result.recommendations)}")
        print(f"   推理长度: {len(result.reasoning)} 字符")
        
        # 检查是否使用了分块策略
        if "chunked" in result.reasoning.lower() or "group" in result.reasoning.lower():
            print("   🔄 使用了数据分块策略")
        else:
            print("   📝 使用了标准分析流程")
            
        return True
        
    except Exception as e:
        print(f"❌ 大数据分析失败: {e}")
        return False


def test_chunked_analysis_directly():
    """直接测试分块分析功能"""
    print("\n🧪 直接测试分块分析功能...")
    
    decision = create_portfolio_decision()
    large_signals = create_large_analyst_signals()
    
    try:
        result = generate_chunked_reflection_analysis(
            ticker="NVDA",
            decision=decision,
            analyst_signals=large_signals,
            model_name="grok-beta",
            model_provider="QingYun"
        )
        
        print(f"✅ 分块分析成功")
        print(f"   决策质量: {result.decision_quality}")
        print(f"   正确性评分: {result.correctness_score}")
        print(f"   关键洞察数量: {len(result.key_insights)}")
        print(f"   推荐建议数量: {len(result.recommendations)}")
        print(f"   推理长度: {len(result.reasoning)} 字符")
        return True
        
    except Exception as e:
        print(f"❌ 分块分析失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试反思分析师处理所有分析师数据的能力\n")
    
    # 运行测试
    tests = [
        ("正常反思分析", test_normal_reflection),
        ("大数据量反思分析", test_large_data_reflection),
        ("直接分块分析", test_chunked_analysis_directly)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "="*60)
    print("📊 测试结果总结:")
    print("="*60)
    
    passed = 0
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"   {test_name}: {status}")
        if success:
            passed += 1
    
    print(f"\n总体结果: {passed}/{len(results)} 测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！反思分析师已准备好处理所有分析师的数据。")
    else:
        print("⚠️  部分测试失败，需要进一步调试。")
    
    return passed == len(results)


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
