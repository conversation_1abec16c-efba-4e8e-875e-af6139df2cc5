{"error_type": "ValueError", "error_message": "Failed to extract valid JSON from response: ```json\n{\n  \"decision_quality\": \"good\",\n  \"correctness_score\": 85,\n  \"key_insights\": [\n    \"The hold decision aligns with mixed analyst signals, reflecting a balanced consideration of bullish, bearish...", "model_name": "grok-beta", "model_provider": "QingYun", "agent_name": "reflection_analyst", "attempt_count": 3}