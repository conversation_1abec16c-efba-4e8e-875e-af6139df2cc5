#!/usr/bin/env python3
"""
Grok-3-Reasoner模型专用测试脚本

该脚本专门用于测试QingYun API提供的grok-3-reasoner模型的功能和性能。
主要测试场景包括：
1. 基本连接测试
2. 复杂推理能力测试
3. 金融分析能力测试
4. 多代理场景兼容性测试
5. 错误处理和恢复能力测试
"""

import os
import sys
import time
import json
from typing import Dict, Any, List
from dotenv import load_dotenv

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.llm.models import get_model, ModelProvider, get_model_info
from src.utils.llm import call_llm
from pydantic import BaseModel, Field
from langchain_core.prompts import ChatPromptTemplate


class FinancialAnalysis(BaseModel):
    """金融分析结果模型"""
    signal: str = Field(description="投资信号: bullish, bearish, neutral")
    confidence: float = Field(description="信心度 (0-100)")
    reasoning: str = Field(description="分析推理过程")
    key_factors: List[str] = Field(description="关键影响因素")
    risk_assessment: str = Field(description="风险评估")


class ReasoningTest(BaseModel):
    """推理测试结果模型"""
    answer: str = Field(description="问题答案")
    reasoning_steps: List[str] = Field(description="推理步骤")
    confidence: float = Field(description="答案信心度 (0-100)")


def test_basic_connection():
    """测试基本连接功能"""
    print("🔗 测试Grok-3-Reasoner基本连接...")
    
    try:
        load_dotenv()
        api_key = os.getenv("QINGYUN_API_KEY")
        
        if not api_key:
            print("❌ QINGYUN_API_KEY未设置")
            return False
        
        # 获取模型
        model = get_model("grok-3-reasoner", ModelProvider.QINGYUN)
        
        # 简单测试
        response = model.invoke("Hello, please respond with 'Connection successful'")
        
        if response and response.content:
            print(f"✅ 连接成功: {response.content[:100]}...")
            return True
        else:
            print("❌ 响应为空")
            return False
            
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False


def test_reasoning_capability():
    """测试复杂推理能力"""
    print("\n🧠 测试Grok-3-Reasoner推理能力...")
    
    try:
        # 创建推理测试提示
        prompt = ChatPromptTemplate.from_messages([
            ("system", """你是一个专业的逻辑推理专家。请仔细分析问题，
            提供详细的推理步骤，并给出最终答案。"""),
            ("human", """逻辑推理题：
            
            在一个小镇上，有三个人：Alice、Bob和Charlie。
            - Alice总是说真话
            - Bob总是说假话  
            - Charlie有时说真话，有时说假话
            
            现在他们对一个事件做出了以下陈述：
            - Alice说："Bob说Charlie是诚实的"
            - Bob说："Charlie说Alice是骗子"
            - Charlie说："我从不说假话"
            
            请分析这些陈述的逻辑关系，并确定每个人的真实身份。
            
            请按照以下JSON格式返回结果：
            {{
                "answer": "你的最终答案",
                "reasoning_steps": ["步骤1", "步骤2", "步骤3"],
                "confidence": 95
            }}""")
        ])
        
        # 使用call_llm进行结构化调用
        result = call_llm(
            prompt=prompt.format_messages(),
            model_name="grok-3-reasoner",
            model_provider="QingYun",
            pydantic_model=ReasoningTest,
            agent_name="reasoning_test"
        )
        
        print(f"✅ 推理测试成功:")
        print(f"   答案: {result.answer}")
        print(f"   推理步骤: {len(result.reasoning_steps)} 步")
        print(f"   信心度: {result.confidence}%")
        
        return True
        
    except Exception as e:
        print(f"❌ 推理测试失败: {e}")
        return False


def test_financial_analysis():
    """测试金融分析能力"""
    print("\n💰 测试Grok-3-Reasoner金融分析能力...")
    
    try:
        # 创建金融分析提示
        prompt = ChatPromptTemplate.from_messages([
            ("system", """你是一位资深的金融分析师，具有深厚的市场分析经验。
            请基于提供的信息进行专业的股票分析。"""),
            ("human", """请分析NVIDIA (NVDA) 股票的投资前景：

            市场信息：
            - 当前股价：$800
            - 52周高点：$950
            - 52周低点：$400
            - P/E比率：65
            - 市值：$2万亿
            
            最新消息：
            - AI芯片需求持续强劲
            - 数据中心业务增长200%
            - 新一代GPU架构发布
            - 地缘政治风险影响供应链
            - 竞争对手加大投入
            
            请提供详细的分析，包括投资信号、风险评估等。
            
            请严格按照以下JSON格式返回：
            {{
                "signal": "bullish/bearish/neutral",
                "confidence": 85,
                "reasoning": "详细分析过程",
                "key_factors": ["因素1", "因素2"],
                "risk_assessment": "风险评估"
            }}""")
        ])
        
        # 使用call_llm进行结构化调用
        formatted_prompt = prompt.format_messages()
        result = call_llm(
            prompt=formatted_prompt,
            model_name="grok-3-reasoner",
            model_provider="QingYun",
            pydantic_model=FinancialAnalysis,
            agent_name="financial_analysis_test"
        )
        
        print(f"✅ 金融分析测试成功:")
        print(f"   投资信号: {result.signal}")
        print(f"   信心度: {result.confidence}%")
        print(f"   关键因素: {len(result.key_factors)} 个")
        print(f"   推理长度: {len(result.reasoning)} 字符")
        
        return True
        
    except Exception as e:
        print(f"❌ 金融分析测试失败: {e}")
        return False


def test_error_handling():
    """测试错误处理能力"""
    print("\n🛡️ 测试Grok-3-Reasoner错误处理...")
    
    try:
        # 测试无效请求
        model = get_model("grok-3-reasoner", ModelProvider.QINGYUN)
        
        # 发送一个可能导致错误的请求
        response = model.invoke("请返回一个非常长的响应" * 1000)  # 可能触发长度限制
        
        print("✅ 错误处理测试通过（未触发错误或正确处理了错误）")
        return True
        
    except Exception as e:
        print(f"✅ 错误处理测试通过（正确捕获了错误）: {type(e).__name__}")
        return True


def test_model_info():
    """测试模型信息获取"""
    print("\n📋 测试Grok-3-Reasoner模型信息...")
    
    try:
        model_info = get_model_info("grok-3-reasoner", "QingYun")
        
        if model_info:
            print(f"✅ 模型信息获取成功:")
            print(f"   显示名称: {model_info.display_name}")
            print(f"   模型名称: {model_info.model_name}")
            print(f"   提供商: {model_info.provider}")
            print(f"   支持JSON模式: {model_info.has_json_mode()}")
            print(f"   是否QingYun模型: {model_info.is_qingyun()}")
            return True
        else:
            print("❌ 无法获取模型信息")
            return False
            
    except Exception as e:
        print(f"❌ 模型信息测试失败: {e}")
        return False


def run_comprehensive_test():
    """运行综合测试"""
    print("🚀 开始Grok-3-Reasoner综合测试")
    print("=" * 60)
    
    test_results = {}
    
    # 运行所有测试
    tests = [
        ("基本连接", test_basic_connection),
        ("模型信息", test_model_info),
        ("推理能力", test_reasoning_capability),
        ("金融分析", test_financial_analysis),
        ("错误处理", test_error_handling),
    ]
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            test_results[test_name] = result
            time.sleep(2)  # 避免API限制
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            test_results[test_name] = False
    
    # 输出测试总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    
    passed = sum(1 for result in test_results.values() if result)
    total = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！Grok-3-Reasoner模型已准备就绪。")
    elif passed >= total * 0.8:
        print("⚠️ 大部分测试通过，模型基本可用，但可能存在一些问题。")
    else:
        print("🚨 多个测试失败，请检查配置和网络连接。")
    
    return test_results


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Grok-3-Reasoner模型测试工具")
    parser.add_argument("--connection", "-c", action="store_true", help="仅测试连接")
    parser.add_argument("--reasoning", "-r", action="store_true", help="仅测试推理能力")
    parser.add_argument("--financial", "-f", action="store_true", help="仅测试金融分析")
    parser.add_argument("--info", "-i", action="store_true", help="仅测试模型信息")
    
    args = parser.parse_args()
    
    if args.connection:
        test_basic_connection()
    elif args.reasoning:
        test_reasoning_capability()
    elif args.financial:
        test_financial_analysis()
    elif args.info:
        test_model_info()
    else:
        run_comprehensive_test()


if __name__ == "__main__":
    main()
