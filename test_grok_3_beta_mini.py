#!/usr/bin/env python3
"""
测试 grok-3-beta-mini 模型
"""

import os
import sys
from dotenv import load_dotenv

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.llm.models import get_model, ModelProvider
from langchain_core.messages import HumanMessage


def test_grok_3_beta_mini():
    """测试 grok-3-beta-mini 模型"""
    print("🧪 测试 QingYun API 的 grok-3-beta-mini 模型")
    print("=" * 50)
    
    # 加载环境变量
    load_dotenv()
    
    # 检查API密钥
    api_key = os.getenv("QINGYUN_API_KEY")
    if not api_key:
        print("❌ QINGYUN_API_KEY环境变量未设置。")
        return False
    
    print(f"✅ API密钥已配置")
    
    model_name = "grok-beta"
    
    try:
        print(f"\n🔍 测试模型: {model_name}")
        
        # 初始化模型
        llm = get_model(model_name, ModelProvider.QINGYUN)
        print(f"✅ 模型初始化成功")
        
        # 测试简单对话
        test_message = "Hello, please introduce yourself in one sentence."
        messages = [HumanMessage(content=test_message)]
        
        print(f"📤 发送测试消息: {test_message}")
        
        response = llm.invoke(messages)
        content = response.content if hasattr(response, 'content') else str(response)
        
        print(f"✅ 模型响应成功")
        print(f"📝 响应内容: {content}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型测试失败: {e}")
        return False


if __name__ == "__main__":
    test_grok_3_beta_mini()
